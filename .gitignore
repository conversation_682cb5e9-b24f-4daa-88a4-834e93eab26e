# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local
# dependencies
/node_modules
/.pnp
.pnp.js
.github/workflows/*

# testing
/coverage

.env

# misc

.env.local
.env.development.local
.env.test.local
.env.production.local


# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.github/workflows/node.js.yml
.github/workflows/dev.yml
*.yml

# vite
.vite.config.js