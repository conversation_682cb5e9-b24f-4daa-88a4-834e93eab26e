import { useEffect, useState, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import PropTypes from "prop-types";
import { Formik, Form, ErrorMessage, Field, useFormikContext } from "formik";

import {
  createTournamentOwner,
  updateTournamentOwner,
} from "../../redux/tournament/tournamentOrganiserActions";
import { resetGlobalLocation } from "../../redux/Location/locationSlice";
import { toggleOrganiserModal } from "../../redux/tournament/tournamentOrganiserSlice";
import { showSuccess } from "../../redux/Success/successSlice";
import { getAll_TO } from "../../redux/tournament/tournamentActions";

import { Dialog, DialogBackdrop, DialogPanel } from "@headlessui/react";
import { IoCloseSharp } from "react-icons/io5";

import TextError from "../Error/formError";
import Button from "./Button";
import ErrorBanner from "./ErrorBanner";
import LocationSearchInput from "./LocationSearch";
import Spinner from "./Spinner";
import axiosInstance from "../../Services/axios";

import {
  TournamentOragniserModalTitle,
  passRegex,
  rowsInOnePage,
} from "../../Constant/app";

export const TournamentOrganiserCreation = ({
  dispatch,
  isOpen,
  initialValues,
  location,
  validationSchema,
  organiserId,
  actionPending,
}) => {
  const [searchParams] = useSearchParams();
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [brandLogoImageError, setBrandLogoImageError] = useState("");
  const brandLogoFileInputRef = useRef(null);
  const modalContentRef = useRef(null);
  const uploadImageToS3 = async (file) => {
    try {
      const formData = new FormData();
      formData.append("uploaded-file", file);

      const config = {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      };
      const response = await axiosInstance.post(
        `${import.meta.env.VITE_BASE_URL}/upload-file`,
        formData,
        config
      );
      return { success: true, url: response.data.data.url };
    } catch (error) {
      return {
        success: false,
        message: error?.response?.data?.message || "Image upload failed",
      };
    }
  };

  const handleBrandLogoImageChange = async (event, setFieldValue) => {
    const file = event.target.files[0];
    if (file) {
      setBrandLogoImageError("");
      
      // Check file size (5MB = 5 * 1024 * 1024 bytes)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        setBrandLogoImageError("File size should not exceed 5MB");
        setFieldValue("ownerDetails.brandLogoImage", "");
        return false;
      }

      const imageUrl = await uploadImageToS3(file);
      if (imageUrl.success) {
        setFieldValue("ownerDetails.brandLogoImage", imageUrl.url);
      } else {
        setBrandLogoImageError(imageUrl.message);
        setFieldValue("ownerDetails.brandLogoImage", "");
      }
      return imageUrl.success;
    }
    return false;
  };

  const scrollToTop = () => {
    if (modalContentRef.current) {
      modalContentRef.current.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  };

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      setSubmitting(true);
      setHasError(false);
      setErrorMessage("");
      let updatedValues;

      const valuesWithLogo = values;

      if (organiserId) {
        const { password } = valuesWithLogo;
        if (password) {
          const isValid = passRegex.test(password);
          if (!isValid) {
            setFieldError(
              "password",
              "Password must have at least 8 characters, including uppercase, lowercase, a number, and a special character."
            );
            setSubmitting(false);
            return;
          }
          updatedValues = valuesWithLogo;
        } else {
          const { password, ...restOfValues } = valuesWithLogo;
          updatedValues = restOfValues;
        }
      } else {
        updatedValues = valuesWithLogo;
      }

      if (updatedValues.ownerDetails) {
        if (
          !updatedValues.ownerDetails.brandLogoImage ||
          updatedValues.ownerDetails.brandLogoImage === null ||
          updatedValues.ownerDetails.brandLogoImage === ""
        ) {
          const { brandLogoImage, ...restOwnerDetails } =
            updatedValues.ownerDetails;
          updatedValues.ownerDetails = restOwnerDetails;
        }
      }

      const result = !organiserId
        ? await dispatch(createTournamentOwner(updatedValues)).unwrap()
        : await dispatch(
            updateTournamentOwner({
              formData: updatedValues,
              ownerId: organiserId,
            })
          ).unwrap();

      if (!result.responseCode) {
        dispatch(
          showSuccess({
            message: !organiserId
              ? "Tournament Organiser created successfully."
              : "Tournament Organiser updated successfully.",
            onClose: "hideSuccess",
          })
        );
        dispatch(
          getAll_TO({
            currentPage: searchParams.get("page") || 1,
            limit: rowsInOnePage,
          })
        );

        // Clear organiserId from URL after successful save
        // const newSearchParams = new URLSearchParams(searchParams);
        // newSearchParams.delete("organiserId");
        // console.log("new search params:",newSearchParams);
        setTimeout(() => {
          dispatch(toggleOrganiserModal());
        }, 300);
      }
    } catch (err) {
      scrollToTop();
      console.log("Error occured while creating the tournament organiser", err);

      setErrorMessage(
        err.data?.message ||
          "Oops! something went wrong while creating the organiser. Please try again."
      );

      setHasError(true);
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setHasError(false);
      setErrorMessage("");
      dispatch(resetGlobalLocation());
      setBrandLogoImageError("");
    }
  }, [isOpen, dispatch]);

  return (
    <Dialog
      open={isOpen}
      onClose={() => dispatch(toggleOrganiserModal())}
      className="relative z-10 "
    >
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-500/75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-[11] w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel
            transition
            className="relative max-h-[90vh] transform overflow-y-auto rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in  w-full max-w-xs sm:max-w-md lg:max-w-[40%]  sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
            ref={modalContentRef}
          >
            <div className="flex w-full">
              {actionPending && (
                <div className="flex items-center justify-center h-full w-full">
                  <Spinner />
                </div>
              )}
              {!actionPending && (
                <div className="flex flex-col justify-between flex-1 items-between gap-3 w-full">
                  <OrganiserModalTitle
                    onCancel={() => dispatch(toggleOrganiserModal())}
                  />

                  {hasError && <ErrorBanner message={errorMessage} />}
                  <Formik
                    enableReinitialize={true}
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                  >
                    {({ isSubmitting, setFieldValue, values }) => {
                      return (
                        <Form>
                          <div className="flex flex-col justify-between w-full gap-4 flex-1">
                            <OrganiserBasicDetails />
                            <OrganiserPhoneAndPassword />
                            <OrganiserTmsAccess />
                            <BrandEmailAndPhone
                              handleBrandLogoImageChange={(event) =>
                                handleBrandLogoImageChange(event, setFieldValue)
                              }
                              brandLogoImage={
                                values.ownerDetails?.brandLogoImage
                              }
                              brandLogoImageError={brandLogoImageError}
                              brandLogoFileInputRef={brandLogoFileInputRef}
                              setFieldValue={setFieldValue}
                            />
                            <BrandPhoneAndLocation />
                            <OrganiserAddress location={location} />

                            <Button
                              className="w-[148px] h-[40px] rounded-[10px] shadow-md bg-[#1570EF] text-[14px] leading-[17px] text-[#FFFFFF] ml-auto"
                              type="submit"
                              loading={isSubmitting}
                            >
                              Save
                            </Button>
                          </div>
                        </Form>
                      );
                    }}
                  </Formik>
                </div>
              )}
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
};
export const OrganiserModalTitle = ({ onCancel }) => {
  return (
    <div className="flex justify-between items-center w-full">
      <div className="flex gap-3">
        <p className="text-md md:text-lg text-[#343C6A] font-semibold">
          {TournamentOragniserModalTitle}
        </p>
      </div>
      <IoCloseSharp
        className="w-[24px] h-[24px] shadow-md cursor-pointer"
        onClick={onCancel}
      />
    </div>
  );
};

const OrganiserBasicDetails = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-[30px] w-full">
      <div className="flex flex-col items-start gap-2.5 w-full">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="name"
        >
          Name
        </label>
        <Field
          placeholder="Enter Organiser Name"
          id="name"
          name="name"
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[6vh] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="name" component={TextError} />
      </div>
      <div className="flex flex-col items-start gap-2.5 w-full">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="email"
        >
          Email
        </label>

        <Field
          placeholder="Enter Organiser Email"
          id="email"
          name="email"
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[6vh] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="email" component={TextError} />
      </div>
    </div>
  );
};

const OrganiserPhoneAndPassword = () => {
  const handlePhoneInput = (e) => {
    const value = e.target.value.replace(/\D/g, "");
    const truncatedValue = value.slice(0, 10);
    e.target.value = truncatedValue;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px] w-full">
      <div className="flex flex-col items-start gap-2.5">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="phone"
        >
          Phone
        </label>
        <Field
          placeholder="Enter Organiser Phone"
          id="phone"
          name="phone"
          type="tel"
          onInput={handlePhoneInput}
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[6vh] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="phone" component={TextError} />
      </div>
      <div className="flex flex-col items-start gap-2.5 w-full flex-wrap">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="password"
        >
          Password
        </label>

        <Field
          placeholder="Enter Organiser Password"
          id="password"
          name="password"
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[6vh] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="password" component={TextError} />
      </div>
    </div>
  );
};
const OrganiserTmsAccess = () => {
  return (
    <>
      <div className="flex items-start gap-2 w-full cursor-pointer">
        <Field
          type="checkbox"
          id="haveFullAccess"
          name="haveFullAccess"
          className="form-checkbox h-4 sm:h-5 w-4 sm:w-5 text-blue-600 cursor-pointer"
        />
        <label
          className=" text-[#232323] text-base leading-[19.36px] cursor-pointer"
          htmlFor="haveFullAccess"
        >
          Give Full Tms Access
        </label>
        <ErrorMessage name="email" component={TextError} />
      </div>
    </>
  );
};
const BrandEmailAndPhone = ({
  handleBrandLogoImageChange,
  brandLogoImage,
  brandLogoImageError,
  brandLogoFileInputRef,
  setFieldValue,
}) => {
  const [isUploading, setIsUploading] = useState(false);

  const handleImageChange = async (event) => {
    setIsUploading(true);
    await handleBrandLogoImageChange(event);
    setIsUploading(false);
    if (brandLogoFileInputRef.current) {
      brandLogoFileInputRef.current.value = "";
    }
  };

  const handleRemoveImage = () => {
    setFieldValue("ownerDetails.brandLogoImage", "");
    if (brandLogoFileInputRef.current) {
      brandLogoFileInputRef.current.value = "";
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px] w-full">
      <div className="flex flex-col items-start gap-2.5">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="ownerDetails.brandName"
        >
          Brand Name
        </label>
        <Field
          placeholder="Enter Organiser Brand Name"
          id="ownerDetails.brandName"
          name="ownerDetails.brandName"
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[6vh] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="ownerDetails.brandName" component={TextError} />

        {/* --- Brand Logo Image Upload Section --- */}
        <div className="w-full mt-2">
          <label className="block text-[#232323] text-base leading-[19.36px] mb-2">
            Brand Logo Image
          </label>

          <input
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            className="hidden"
            id="brandLogoImageUpload"
            ref={brandLogoFileInputRef}
          />

          {!brandLogoImage ? (
            <label
              htmlFor="brandLogoImageUpload"
              className="flex flex-col items-center justify-center w-full h-[150px] border border-dashed border-gray-300 rounded-md cursor-pointer bg-gray-50 hover:bg-gray-100 transition duration-200"
            >
              {isUploading ? (
                <div className="flex flex-col items-center justify-center">
                  <div className="w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                  <p className="text-sm text-gray-600">Uploading image...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <svg
                    className="w-8 h-8 mb-3 text-blue-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    ></path>
                  </svg>
                  <p className="mb-1 text-sm text-blue-500">Click to upload</p>
                  <p className="text-xs text-gray-500 mt-1">
                    (Max. File size: 5MB)
                  </p>
                  <p className="text-xs text-gray-500">(Image Size: 300x300)</p>
                </div>
              )}
            </label>
          ) : (
            <div className="relative w-full h-[150px] border border-gray-300 rounded-md overflow-hidden">
              <img
                src={brandLogoImage}
                alt="Brand Logo"
                className="w-full h-full object-contain"
              />
              <button
                type="button"
                onClick={handleRemoveImage}
                className="absolute top-2 right-2 bg-gray-800 text-white rounded-full p-1 shadow-md hover:bg-gray-900 transition-colors"
              >
                <IoCloseSharp size={16} />
              </button>
            </div>
          )}

          {brandLogoImageError && (
            <p className="text-red-500 text-xs mt-1">{brandLogoImageError}</p>
          )}
        </div>
      </div>
      <div className="flex flex-col items-start gap-2.5 w-full">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="ownerDetails.brandEmail"
        >
          Brand Email
        </label>

        <Field
          placeholder="Enter Organiser Brand Email"
          id="ownerDetails.brandEmail"
          name="ownerDetails.brandEmail"
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[6vh] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="ownerDetails.brandEmail" component={TextError} />
      </div>
    </div>
  );
};

const BrandPhoneAndLocation = () => {
  const handlePhoneInput = (e) => {
    const value = e.target.value.replace(/\D/g, "");
    const truncatedValue = value.slice(0, 10);
    e.target.value = truncatedValue;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px] w-full">
      <div className="flex flex-col items-start gap-2.5">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="ownerDetails.brandPhone"
        >
          Brand Phone
        </label>
        <Field
          placeholder="Enter Brand Phone"
          id="ownerDetails.brandPhone"
          name="ownerDetails.brandPhone"
          type="tel"
          onInput={handlePhoneInput}
          className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <ErrorMessage name="ownerDetails.brandPhone" component={TextError} />
      </div>
      <div className="flex flex-col items-start gap-2.5 w-full">
        <label
          className=" text-[#232323] text-base leading-[19.36px]"
          htmlFor="ownerDetails.address.location"
        >
          Google Map
        </label>

        <LocationSearchInput
          id="ownerDetails.address.location"
          name="ownerDetails.address.location"
        />
        <ErrorMessage
          name="ownerDetails.address.location.coordinates"
          component={TextError}
        />
      </div>
    </div>
  );
};

const OrganiserAddress = ({ location }) => {
  const { setFieldValue } = useFormikContext();

  const handlePostalCodeInput = (e) => {
    const value = e.target.value.replace(/\D/g, "");
    const truncatedValue = value.slice(0, 6);
    e.target.value = truncatedValue;
  };

  useEffect(() => {
    if (location && (location.city || location.state)) {
      setFieldValue(
        "ownerDetails.address.location.coordinates[0]",
        location?.lng || 0
      );
      setFieldValue(
        "ownerDetails.address.location.coordinates[1]",
        location?.lat || 0
      );
      setFieldValue(
        "ownerDetails.address.line1",
        location?.address_line1 || ""
      );
      setFieldValue(
        "ownerDetails.address.line2",
        location?.address_line2 || ""
      );
      setFieldValue("ownerDetails.address.city", location?.city || "");
      setFieldValue("ownerDetails.address.state", location?.state || "");
      setFieldValue(
        "ownerDetails.address.postalCode",
        location?.pin_code || ""
      );

      setFieldValue("ownerDetails.address.location.type", "Point");
    }
  }, [
    location?.lat,
    location?.lng,
    location?.city,
    location?.state,
    location?.pin_code,
    location?.address_line1,
    location?.address_line2,
    setFieldValue,
  ]);
  return (
    <div className="flex flex-col items-start gap-2.5">
      <p className=" text-base leading-[19.36px] text-[#232323]">Address</p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2.5 w-full">
        <div className="flex flex-col items-start gap-2.5">
          <label
            className="text-xs text-[#232323]"
            htmlFor="ownerDetails.address.line1"
          >
            Line 1
          </label>
          <Field
            placeholder="Enter Line 1 Address"
            id="ownerDetails.address.line1"
            name="ownerDetails.address.line1"
            className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <ErrorMessage
            name="ownerDetails.address.line1"
            component={TextError}
          />
        </div>
        <div className="flex flex-col items-start gap-2.5">
          <label
            className="text-xs text-[#232323]"
            htmlFor="ownerDetails.address.line2"
          >
            Line 2
          </label>
          <Field
            placeholder="Enter Line 2 Address"
            id="ownerDetails.address.line2"
            name="ownerDetails.address.line2"
            className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <ErrorMessage
            name="ownerDetails.address.line2"
            component={TextError}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2.5 w-full">
        <div className="flex flex-col items-start gap-2.5">
          <label
            className="text-xs text-[#232323]"
            htmlFor="ownerDetails.address.city"
          >
            City
          </label>
          <Field
            placeholder="Enter City "
            id="ownerDetails.address.city"
            name="ownerDetails.address.city"
            className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <ErrorMessage
            name="ownerDetails.address.city"
            component={TextError}
          />
        </div>
        <div className="flex flex-col items-start gap-2.5">
          <label
            className="text-xs text-[#232323]"
            htmlFor="ownerDetails.address.state"
          >
            State
          </label>
          <Field
            placeholder="Enter State"
            id="ownerDetails.address.state"
            name="ownerDetails.address.state"
            className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <ErrorMessage
            name="ownerDetails.address.state"
            component={TextError}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2.5 w-full">
        <div className="flex flex-col items-start gap-2.5">
          <label
            className="text-xs text-[#232323]"
            htmlFor="ownerDetails.address.postalCode"
          >
            Postal Code
          </label>
          <Field
            placeholder="Enter PostalCode"
            id="ownerDetails.address.postalCode"
            name="ownerDetails.address.postalCode"
            type="tel"
            onInput={handlePostalCodeInput}
            className="w-full px-[19px] border-[1px] border-[#DFEAF2] rounded-[15px] h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <ErrorMessage
            name="ownerDetails.address.postalCode"
            component={TextError}
          />
        </div>
      </div>
    </div>
  );
};

OrganiserAddress.propTypes = {
  location: PropTypes.object,
};

OrganiserModalTitle.propTypes = {
  onCancel: PropTypes.func,
};

TournamentOrganiserCreation.propTypes = {
  owner: PropTypes.object,
  dispatch: PropTypes.func,
  isOpen: PropTypes.bool,
  initialValues: PropTypes.object,
  location: PropTypes.object,
  validationSchema: PropTypes.object,
  organiserId: PropTypes.string,
  actionPending: PropTypes.bool,
};

export default function TournamentOrganiserModal({
  isOpen,
  onClose,
  fetchHomepageSections,
}) {
  const [imagePreview, setImagePreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [initialState, setInitialState] = useState(initialValues);

  useEffect(() => {
    if (!isOpen) {
      // Reset form state when modal is closed
      setImagePreview(null);
      setInitialState(initialValues);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-10">
      <DialogBackdrop className="fixed inset-0 bg-gray-500/75 transition-opacity" />
      <div className="fixed inset-0 z-10 w-screen">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <DialogPanel className="relative max-h-[90vh] transform overflow-y-auto rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
            <Formik
              enableReinitialize
              initialValues={initialState}
              validationSchema={validationSchema}
              onSubmit={async (values, { setSubmitting, resetForm }) => {
                try {
                  setSubmitting(true);
                  resetForm();
                  setInitialState(initialValues);
                  onClose();
                } catch (error) {
                  setSubmitting(false);
                  resetForm();
                  setInitialState(initialValues);
                  onClose();
                }
              }}
            ></Formik>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
}
