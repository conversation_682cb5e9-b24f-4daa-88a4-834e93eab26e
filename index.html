<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.ico" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <!-- <link
      href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@400&display=swap"
      rel="stylesheet"
    /> -->
    <link rel="icon" type="image/svg+xml" href="https://picklebaystaging.s3.ap-south-1.amazonaws.com/cms/uploaded-file/1743937166208-196437423-1000127021.jpg"/>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/brackets-viewer@latest/dist/brackets-viewer.min.css"
    />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <!-- <link rel="manifest" href="%PUBLIC_URL%/manifest.json" /> -->

    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Picklebay admin</title>

  </head>
  <body>
    <div id="root"></div>
    <div id="portal-drawer"></div>

    <script type="module" src="/src/main.jsx"></script>
    <script src="https://unpkg.com/webtonative@1.0.73/webtonative.min.js"></script>
    <script
      type="text/javascript"
      src="https://cdn.jsdelivr.net/npm/brackets-viewer@latest/dist/brackets-viewer.min.js"
    ></script>
  </body>
</html>
