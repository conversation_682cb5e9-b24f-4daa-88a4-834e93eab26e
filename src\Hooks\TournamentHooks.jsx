import { useQuery } from "@tanstack/react-query"
import { getAllTournaments, searchTournament } from "../api/tournament"


export const useGetAllTournaments = ({ownerId, pageParam, limit, ...filters}) => {
  return useQuery({
    queryKey: ['getAllTournaments', ownerId, pageParam, limit, filters],
    queryFn: () => getAllTournaments({ ownerId, pageParam, limit, filters }),
  })
}

export const useSearchTournament = ({ pageParam, limit, search, ownerId, ...filters }) => {
  return useQuery({
    queryKey: ['searchTournament', pageParam, limit, search, ownerId, filters],
    queryFn: () => searchTournament({ pageParam, limit, search, ownerId, filters }),
    enabled: !!search,
  });
};
