/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,js,jsx,ts,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        "author": ['Author', "Arial", "Helvetica", "Tahoma", "Verdana", "Trebuchet MS", "Geneva", "sans-serif" ],
        "author-italic": ['Author-Italic', "Arial", "Helvetica", "Tahoma", "Verdana", "Trebuchet MS", "Geneva", "sans-serif" ],
        "general": ['GeneralSans', "Arial", "Helvetica", "Tahoma", "Verdana", "Trebuchet MS", "Geneva", "sans-serif" ],
        "general-medium": ['GeneralSans-Medium', "Arial", "Helvetica", "Tahoma", "Verdana", "Trebuchet MS", "Geneva", "sans-serif" ],
        "general-semibold": ['GeneralSans-SemiBold', "Arial", "Helvetica", "Tahoma", "Verdana", "Trebuchet MS", "Geneva", "sans-serif" ],
        "general-italic": ['GeneralSans-Italic', "Arial", "Helvetica", "Tahoma", "Verdana", "Trebuchet MS", "Geneva", "sans-serif" ]
      },
      spacing: {
        30: "30px",
      },
      colors: {
        customColor: "#232323",
        tour_List_Color: "#718EBF",
        player_table: "#667085",
        matchTextColor: "#343C6A",
        matchModalTextColor: "#8D8D8D",
        "1c0e0eb3": "#1C0E0EB3",
        white: "#FFFFFF",
        richBlue: {
          5: "#1570EF",
          600: "#19367F",
        },
        '383838': "#383838",
        '718ebf': "#718EBF",
        grey: {
          100: "#718EBF",
          200: "#F7F9FC",
          300: "#667085",
          500: "#667085",
          600: "#101828",
          900: "#2B2F38",
        },
      },
      backgroundColor: {
        customColor: "#FFFFFF",
      },
      screens: {
        portrait: { raw: "(orientation: portrait)" },
        landscape: { raw: "(orientation: landscape)" },
        ipad_air: "820px",
        tab: "920px",
        xs: "480px",
        xl: "1280px",
        "2xl": "1536px",
      },
      animation: {
        spin: "spin 1s linear infinite",
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities({
        ".scrollbar-hide": {
          "-ms-overflow-style": "none", // Hide scrollbar in IE and Edge
          "scrollbar-width": "none", // Hide scrollbar in Firefox
          "&::-webkit-scrollbar": {
            display: "none", // Hide scrollbar in Chrome, Safari, and Edge
          },
        },
      });
    },
  ],
};
