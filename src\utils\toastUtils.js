import toast from 'react-hot-toast';

const createToast = (message) => {
  if (typeof message === 'object') {
    message = message.message || 'Operation completed successfully';
  }
  toast.success(message, {
    duration: 3000,
    position: 'bottom-center',
    style: {
      borderRadius: '10px',
      color: '#383838',
      backgroundColor: '#ffffff',
      fontFamily: 'GeneralSans, Arial, Helvetica, Tahoma, Verdana, Trebuchet MS, Geneva, sans-serif',
      fontSize: '14px',
      fontWeight: '500',
      padding: '10px',
    },
  });
};

const createErrorToast = (message) => {
  if (typeof message === 'object') {
    message = message.message || 'An error occurred';
  }
  toast.error(message, {
    duration: 3000,
    position: 'bottom-center',
    style: {
      borderRadius: '10px',
      color: '#383838',
      backgroundColor: '#ffffff',
      fontFamily: 'GeneralSans, Arial, Helvetica, Tahoma, Verdana, Trebuchet MS, Geneva, sans-serif',
      fontSize: '14px',
      fontWeight: '500',
      padding: '10px',
    },
  });
};

export { createToast, createErrorToast };
