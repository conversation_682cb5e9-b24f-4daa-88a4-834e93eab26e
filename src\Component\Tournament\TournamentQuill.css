/* Ensure Quill editor fits its parent and does not overflow horizontally */
.custom-quill,
.custom-quill .ql-container,
.custom-quill .ql-editor {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  box-sizing: border-box;
  overflow-x: hidden;
}

.custom-quill .ql-container.ql-snow {
  height: 200px !important;
  max-height: 300px;
  overflow-y: auto;
}

.custom-quill .ql-editor {
  min-height: 120px;
  height: 100%;
  max-height: none;
  overflow-y: visible;
}

@media screen and (max-width: 768px) {
  .custom-quill .ql-toolbar.ql-snow {
    overflow-y: auto;
    justify-content: flex-start !important;
  }
  .custom-quill .ql-container.ql-snow {
    height: 150px !important;
    max-height: 200px;
  }
}