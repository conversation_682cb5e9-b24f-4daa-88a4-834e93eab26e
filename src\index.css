@font-face {
  font-family: "Public Sans";
  src: url("./Assests/fonts/PublicSans-Regular.ttf") format("trueType");

  font-weight: 400;
  font-style: normal;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: "Public Sans";
  }
}
* {
  @apply box-border;
}

@layer {
  th,
  td {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.tab-button.active::after {
  position: absolute;
  content: "";
  bottom: 0;
  left: 0;
  width: 100%;

  @apply border-b-[3px] border-[#5B8DFF] rounded-tl-[10px]
    rounded-tr-[10px];
}

.tab-button {
  @apply relative text-[#686868] font-[500] leading-[19.36px] text-base pb-4;
}

.tab-button.active {
  @apply text-[#5B8DFF];
}

.tab-button {
  @apply relative text-[#686868] font-[500] leading-[19.36px] text-base pb-4;
}

.tab-button.active {
  @apply text-[#5B8DFF];
}

/* Customize the editor container */
#description .ql-container {
  @apply bg-white border-2 border-[#DFEAF2] rounded-md p-2 text-gray-800 text-base;
}

/* Customize the toolbar */
#description .ql-toolbar {
  @apply flex justify-center gap-5 bg-[#DFEAF2] border-2 border-[#DFEAF2] rounded-t-md;
}

/* Style the toolbar buttons */
#description .ql-toolbar button {
  @apply bg-white text-gray-800 border border-[#DFEAF2] rounded-md hover:bg-gray-300;
}

/* Style the editor text */
#description .ql-editor {
  @apply min-h-[170px] text-gray-900 font-sans leading-relaxed;
}

/* Customize editor focus state */
#description .ql-container:focus-within {
  @apply border-blue-500 shadow-md;
}

#description .ql-toolbar .ql-formats {
  @apply flex gap-3 mx-2; /* Adjust gap and margin as needed */
}

/* Customize the editor container */
#preRequisites .ql-container {
  @apply bg-white border-2 border-[#DFEAF2] rounded-md p-2 text-gray-800 text-base;
}

/* Customize the toolbar */
#preRequisites .ql-toolbar {
  @apply flex justify-center gap-5 bg-[#DFEAF2] border-2 border-[#DFEAF2] rounded-t-md;
}

/* Style the toolbar buttons */
#preRequisites .ql-toolbar button {
  @apply bg-white text-gray-800 border border-[#DFEAF2] rounded-md hover:bg-gray-300;
}

/* Style the editor text */
#preRequisites .ql-editor {
  @apply min-h-[170px] text-gray-900 font-sans leading-relaxed;
}

/* Customize editor focus state */
#preRequisites .ql-container:focus-within {
  @apply border-blue-500 shadow-md;
}

#preRequisites .ql-toolbar .ql-formats {
  @apply flex gap-3 mx-2; /* Adjust gap and margin as needed */
}


.brackets-viewer table {
  display: none;
}
