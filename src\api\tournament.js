import axiosInstance from "../Services/axios";
import { API_END_POINTS } from "../Constant/routes";

/* 
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ // Fetching all tournaments                                             │
  └─────────────────────────────────────────────────────────────────────────┘
 */

export const getAllTournaments = async ({ ownerId, pageParam = 1, limit = 10, filters = {} }) => {
  const TOURNAMENT_END_POINT = API_END_POINTS.tournament.GET.getAllTouranaments(ownerId);
  const ENDPOINT = `${import.meta.env.VITE_BASE_URL}${TOURNAMENT_END_POINT}`;

  let config = {
    method: 'GET',
    maxBodyLength: Infinity,
    url: ENDPOINT,
    withCredentials: true,
    params: {
      page: pageParam,
      limit: limit,
      ...filters,
    }
  };

  try {
    const response = await axiosInstance.request(config)
    return response.data;
  } catch (error) {
    throw error;
  }
}

/* 
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ // Search tournaments                                                   │
  └─────────────────────────────────────────────────────────────────────────┘
 */

export const searchTournament = async ({ pageParam = 1, limit = 10, search, ownerId, filters = {} }) => {
  const TOURNAMENT_END_POINT = API_END_POINTS.tournament.GET.searchTournaments(ownerId);
  const ENDPOINT = `${import.meta.env.VITE_BASE_URL}${TOURNAMENT_END_POINT}`;

  let config = {
    method: 'GET',
    maxBodyLength: Infinity,
    url: ENDPOINT,
    withCredentials: true,
    params: {
      page: pageParam,
      limit: limit,
      search: search,
      ...filters,
    }
  };

  try {
    const response = await axiosInstance.request(config)
    return response.data;
  } catch (error) {
    throw error;
  }
}