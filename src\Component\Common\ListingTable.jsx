import React, { useState, useEffect, useMemo } from "react";
import { useSearchDebounce } from "../../Hooks/useSearchDebounce";
import { getPageNumbersToDisplay } from "../../utils/tournamentUtils";
import { useSearchParams } from "react-router-dom";

const SkeletonRow = ({ columns }) => (
  <tr className="animate-pulse">
    {columns.map((col, idx) => (
      <td key={idx} className="px-6 py-4">
        <div className="h-4 bg-gray-200 rounded w-full" />
      </td>
    ))}
  </tr>
);

const ListingTable = ({
  enableSearch,
  enableFilter,
  enablePagination,
  showTableHeader,
  queryHook,
  searchHook,
  columns,
  filterOptions,
  ownerId
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialSearch = searchParams.get('search') || '';
  const initialPage = searchParams.get('page') || 1;
  const initialFilterKey = searchParams.get('filter') || filterOptions.find(f => f.defaultActive).key || filterOptions[0].key;
  const initialSubFilterKey = searchParams.get('subFilter') || 'all';

  const [searchTerm, setSearchTerm] = useState(initialSearch);
  const [page, setPage] = useState(parseInt(initialPage) || 1);
  const debouncedSearchTerm = useSearchDebounce(searchTerm, 500);
  const limit = 10;

  const defaultFilter = useMemo(() => {
    return filterOptions.find((f) => f.defaultActive) || filterOptions[0];
  }, [filterOptions]);

  const [activeFilter, setActiveFilter] = useState(defaultFilter);
  const [activeSubFilter, setActiveSubFilter] = useState(initialSubFilterKey);

  const selectedFilter = useMemo(
    () => filterOptions.find((f) => f.key === activeFilter?.key) || {},
    [activeFilter, filterOptions]
  );

  const mergedParams = useMemo(() => {
    const mainParams = selectedFilter?.params || {};
    const subParams =
      selectedFilter?.subFilters?.find((sf) => sf.key === activeSubFilter)
        ?.params || {};
    return { ...mainParams, ...subParams };
  }, [selectedFilter, activeSubFilter]);

  const searchEnabled = !!debouncedSearchTerm;

  const queryParams = {
    pageParam: page,
    limit,
    search: debouncedSearchTerm,
    ...mergedParams,
  };

  const defaultQuery = queryHook({ ownerId, pageParam: page, limit, ...mergedParams });
  const searchQuery = searchHook({ ...queryParams, ownerId });
  const { data, isLoading, error, refetch } = searchEnabled ? searchQuery : defaultQuery;

  const rows = useMemo(() => data?.data?.tournaments || [], [data]);
  const totalPages = useMemo(() => {
    const totalItems = data?.data?.total || 0;
    return Math.ceil(totalItems / limit);
  }, [data]);

  useEffect(() => {
    setPage(1);
  }, [debouncedSearchTerm, activeFilter?.key, activeSubFilter]);

  // Sync page state with URL changes
  useEffect(() => {
    const urlPage = searchParams.get('page');
    if (urlPage && parseInt(urlPage) !== page) {
      setPage(parseInt(urlPage));
    }
  }, [searchParams]);

  useEffect(() => {
    const params = {
      page: page.toString(),
      search: debouncedSearchTerm,
      filter: activeFilter?.key,
      subFilter: activeSubFilter,
    };

    Object.keys(params).forEach((key) => {
      if(!params[key] || params[key] === 'all') {
        delete params[key];
      }
    })
    setSearchParams(params);
  }, [page, debouncedSearchTerm, activeFilter?.key, activeSubFilter]);

  return (
    <div className="w-full space-y-4">
      {/* Search Bar */}
      {enableSearch && (
        <div className="flex items-center lg:max-w-[40%] mr-auto">
          <input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded focus:outline-none font-medium placeholder:text-sm placeholder:text-gray-500"
          />
        </div>
      )}

      {/* Filters */}
      {enableFilter && (
        <>
          <div className="flex flex-wrap gap-2 justify-between bg-white items-center p-2 rounded-md">
            {filterOptions.map((filter) => (
              <button
                key={filter.key}
                onClick={() => {
                  setActiveFilter(filter);
                  const defaultSub =
                    filter.subFilters?.find((sf) => sf.defaultActive)?.key || "all";
                  setActiveSubFilter(defaultSub);
                  setPage(1);
                }}
                className={`${
                  filter.key === activeFilter?.key
                    ? "bg-blue-500 text-white"
                    : "bg-white text-383838"
                } cursor-pointer px-4 py-2 rounded-md font-medium text-sm w-[100px] lg:w-[230px]`}
              >
                {filter.label}
              </button>
            ))}
          </div>

          {selectedFilter?.subFilters && (
            <div className="flex flex-wrap md:justify-end gap-1 md:gap-4 mt-2">
              <p className="text-sm font-medium text-383838">
                {selectedFilter.subFilterLabel}
              </p>
              {selectedFilter.subFilters.map((sub) => (
                <label
                  key={sub.key}
                  className="flex items-center space-x-1 cursor-pointer"
                >
                  <input
                    type="radio"
                    name="upcomingSubFilter"
                    value={sub.key}
                    checked={sub.key === activeSubFilter}
                    onChange={() => setActiveSubFilter(sub.key)}
                    className="peer"
                  />
                  <span className="text-sm font-medium text-gray-900">
                    {sub.label}
                  </span>
                </label>
              ))}
            </div>
          )}
        </>
      )}

      {/* Content Section */}
      <div className="w-full">
        {isLoading ? (
          <div className="hidden lg:block overflow-x-auto w-full">
            <table className="min-w-full table-fixed border-collapse border-spacing-y-4">
              <thead>
                <tr>
                  {columns.map((col) => (
                    <th
                      key={col.key}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider"
                    >
                      {col.label || ""}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white" style={{ borderSpacing: "0 16px" }}>
                {Array.from({ length: 10 }).map((_, i) => (
                  <SkeletonRow key={i} columns={columns} />
                ))}
              </tbody>
            </table>
          </div>
        ) : error ? (
          <div className="text-center text-red-600 py-4 space-y-2">
            <p className="text-sm">Something went wrong while loading data.</p>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-red-100 text-red-700 text-sm rounded hover:bg-red-200"
            >
              Retry
            </button>
          </div>
        ) : rows.length === 0 ? (
          <div className="text-center text-gray-500 py-6">No results found.</div>
        ) : (
          <>
            {/* Table View - Desktop */}
            <div className="hidden lg:block overflow-x-auto w-full">
              <table className="min-w-full table-fixed border-separate" style={{ borderSpacing: '0 16px' }}>
                {showTableHeader && (
                  <thead>
                    <tr>
                      {columns.map((col) => (
                        <th
                          key={col.key}
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        >
                          {col.label || ""}
                        </th>
                      ))}
                    </tr>
                  </thead>
                )}
                <tbody
                  className="bg-white"
                  style={{ borderSpacing: "0 16px" }} // gap-4 equivalent
                >
                  {rows.map((row, rowIndex) => (
                    <tr key={row._id || rowIndex} className="">
                      {columns.map((col) => (
                        <td
                          key={col.key}
                          className={`px-6 py-4 ${
                            col.key === "tour_logo"
                              ? "w-[64px]"
                              : "break-all whitespace-normal max-w-[200px]"
                          }`}
                        >
                          {col.render ? col.render(row) : row[col.key]}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Card View - Mobile */}
            <div className="block lg:hidden space-y-4">
              {rows.map((row, rowIndex) => (
                <div
                  key={row._id || rowIndex}
                  className="p-4 border border-gray-200 rounded-lg shadow-sm bg-white space-y-2"
                >
                  {columns
                    .filter((col) => !col.hideOnMobile)
                    .map((col) => (
                      <div
                        key={col.key}
                        className={`flex justify-between items-start text-sm w-full ${
                          col.key === "button" ? "mt-4" : ""
                        }`}
                      >
                        {col.key !== "button" ? (
                          <>
                            {col.showLabelInMobile !== false && (
                              <span className="text-gray-600 font-medium">
                                {col.label || col.key}
                              </span>
                            )}
                            <span className="text-gray-800 text-right w-full break-all">
                              {col.render ? col.render(row) : row[col.key]}
                            </span>
                          </>
                        ) : (
                          <div className="w-full max-w-max">
                            {col.render ? col.render(row) : null}
                          </div>
                        )}
                      </div>
                    ))}
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Pagination */}
      {enablePagination && totalPages > 1 && (
        <div className="flex justify-between items-center mt-4 flex-wrap gap-y-2">
          <button
            onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
            disabled={page === 1}
            className={`text-sm ${
              page === 1 ? "hidden" : "text-gray-400 hover:text-gray-500"
            }`}
          >
            ← Previous
          </button>

          <div className="flex space-x-2 mx-auto md:gap-3">
            {getPageNumbersToDisplay(page, totalPages).map((pg, idx) =>
              pg === "..." ? (
                <span key={`ellipsis-${idx}`} className="text-gray-500">
                  ...
                </span>
              ) : (
                <button
                  key={`page-${pg}`}
                  onClick={() => setPage(parseInt(pg))}
                  className={`text-sm ${
                    page === parseInt(pg)
                      ? "text-blue-500"
                      : "text-gray-400 hover:text-gray-600"
                  }`}
                >
                  {pg}
                </button>
              )
            )}
          </div>

          <button
            onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
            disabled={page === totalPages}
            className={`text-sm ${
              page === totalPages ? "hidden" : "text-gray-400 hover:text-gray-500"
            }`}
          >
            Next →
          </button>
        </div>
      )}
    </div>
  );
};

export default ListingTable;
