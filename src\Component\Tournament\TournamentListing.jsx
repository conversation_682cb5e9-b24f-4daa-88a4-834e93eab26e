import { TournamentHeaders, TournamentFilters } from "../../Constant/tournament";
import ListingTable from "../Common/ListingTable";
import { useGetAllTournaments, useSearchTournament } from "../../Hooks/TournamentHooks";
import { useSelector } from "react-redux";


const TournamentListingWrapper = () => {
  const ownerId = useSelector((state) => state?.user?.id);
  return (
    <ListingTable enableSearch enableFilter enablePagination queryHook={useGetAllTournaments} searchHook={useSearchTournament} columns={TournamentHeaders} filterOptions={TournamentFilters} ownerId={ownerId}/>
  )
}

export default TournamentListingWrapper;