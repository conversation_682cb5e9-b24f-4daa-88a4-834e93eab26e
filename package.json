{"name": "picklebay_cms_vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "test": "jest", "preview": "vite preview --port 3001 --host"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@ewanmellor/brackets-viewer": "^1.6.911", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.74.7", "axios": "^1.7.9", "formik": "^2.4.6", "js-cookie": "^3.0.5", "motion": "^12.7.3", "nanoid": "^5.1.5", "picklebay_cms_vite": "file:", "prop-types": "^15.8.1", "react": "^18.3.1", "react-awesome-slider": "^4.1.0", "react-cookie": "^7.2.2", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-google-autocomplete": "^2.7.3", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "react-loading-skeleton": "^3.5.0", "react-phone-input-2": "^2.15.1", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.1.1", "react-toastify": "^11.0.5", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@testing-library/jest-dom": "^6.6.3", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}